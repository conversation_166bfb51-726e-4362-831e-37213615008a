import { cva, type VariantProps } from 'class-variance-authority';

export { default as <PERSON><PERSON> } from './Button.vue';

export const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-brutal transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none border-brutal shadow-brutal hover-brutal cursor-brutal",
  {
    variants: {
      variant: {
        default: 'bg-neon-lime text-brutal-black hover-brutal-neon',
        destructive: 'bg-neon-pink text-brutal-white hover-brutal-pink',
        outline: 'bg-brutal-white text-brutal-black hover-brutal',
        secondary: 'bg-neon-cyan text-brutal-black hover-brutal-cyan',
        ghost:
          'bg-transparent text-brutal-black hover:bg-brutal-charcoal hover:text-brutal-white',
        link: 'text-neon-lime underline-offset-4 hover:underline bg-transparent border-0 shadow-none',
      },
      size: {
        default: 'h-9 px-4 py-2 has-[>svg]:px-3',
        sm: 'h-8 gap-1.5 px-3 has-[>svg]:px-2.5',
        lg: 'h-10 px-6 has-[>svg]:px-4',
        icon: 'size-9',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

export type ButtonVariants = VariantProps<typeof buttonVariants>;
