<template>
  <component :is="iconComponent" v-bind="$attrs" />
</template>

<script setup lang="ts">
import { computed } from 'vue'
import * as LucideIcons from 'lucide-vue-next'

interface Props {
  name: string
}

const props = defineProps<Props>()

const iconComponent = computed(() => {
  // Convert kebab-case to PascalCase
  const iconName = props.name
    .split(':')[1] // Remove 'lucide:' prefix
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join('')
  
  return (LucideIcons as any)[iconName] || LucideIcons.HelpCircle
})
</script>
