<script setup lang="ts">
import type { HTMLAttributes } from 'vue';
import { cn } from '@/lib/utils';

const props = defineProps<{
  class?: HTMLAttributes['class'];
}>();
</script>

<template>
  <div
    data-slot="card"
    :class="
      cn(
        'text-card-foreground border-brutal bg-brutal-white shadow-brutal hover-brutal cursor-brutal flex flex-col gap-6 py-6',
        props.class,
      )
    "
  >
    <slot />
  </div>
</template>
