<template>
  <div class="min-h-screen bg-brutal-white">
    <!-- Navigation Header -->
    <header class="border-brutal-heavy bg-brutal-white sticky top-0 z-50">
      <nav class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="flex h-16 items-center justify-between">
          <!-- Logo -->
          <div class="flex items-center">
            <NuxtLink to="/" class="font-brutal text-2xl text-brutal-black hover-brutal">
              DEFI.AI
            </NuxtLink>
          </div>

          <!-- Desktop Navigation -->
          <div class="hidden md:block">
            <div class="ml-10 flex items-baseline space-x-4">
              <NuxtLink
                to="/portfolio"
                class="border-brutal bg-brutal-white px-4 py-2 font-brutal text-sm uppercase text-brutal-black hover-brutal"
              >
                PORTFOLIO
              </NuxtLink>
              <NuxtLink
                to="/strategies"
                class="border-brutal bg-brutal-white px-4 py-2 font-brutal text-sm uppercase text-brutal-black hover-brutal"
              >
                STRATEGIES
              </NuxtLink>
              <NuxtLink
                to="/analytics"
                class="border-brutal bg-brutal-white px-4 py-2 font-brutal text-sm uppercase text-brutal-black hover-brutal"
              >
                ANALYTICS
              </NuxtLink>
              <NuxtLink
                to="/history"
                class="border-brutal bg-brutal-white px-4 py-2 font-brutal text-sm uppercase text-brutal-black hover-brutal"
              >
                HISTORY
              </NuxtLink>
            </div>
          </div>

          <!-- Wallet Connection Button -->
          <div class="flex items-center">
            <Button
              class="border-brutal-thick bg-neon-pink px-6 py-3 font-brutal text-sm uppercase text-brutal-white shadow-brutal hover-brutal"
              @click="connectWallet"
            >
              CONNECT WALLET
            </Button>
          </div>

          <!-- Mobile menu button -->
          <div class="md:hidden">
            <Button
              class="border-brutal bg-brutal-white p-2 text-brutal-black hover-brutal"
              @click="toggleMobileMenu"
            >
              <Icon name="lucide:menu" class="h-6 w-6" />
            </Button>
          </div>
        </div>

        <!-- Mobile Navigation -->
        <div v-if="isMobileMenuOpen" class="md:hidden">
          <div class="border-brutal-thick bg-brutal-white px-2 pb-3 pt-2 sm:px-3">
            <NuxtLink
              to="/portfolio"
              class="border-brutal mb-2 block bg-brutal-white px-3 py-2 font-brutal text-sm uppercase text-brutal-black hover-brutal"
            >
              PORTFOLIO
            </NuxtLink>
            <NuxtLink
              to="/strategies"
              class="border-brutal mb-2 block bg-brutal-white px-3 py-2 font-brutal text-sm uppercase text-brutal-black hover-brutal"
            >
              STRATEGIES
            </NuxtLink>
            <NuxtLink
              to="/analytics"
              class="border-brutal mb-2 block bg-brutal-white px-3 py-2 font-brutal text-sm uppercase text-brutal-black hover-brutal"
            >
              ANALYTICS
            </NuxtLink>
            <NuxtLink
              to="/history"
              class="border-brutal mb-2 block bg-brutal-white px-3 py-2 font-brutal text-sm uppercase text-brutal-black hover-brutal"
            >
              HISTORY
            </NuxtLink>
          </div>
        </div>
      </nav>
    </header>

    <!-- Main Content -->
    <main class="flex-1">
      <slot />
    </main>

    <!-- Footer -->
    <footer class="border-brutal-heavy bg-brutal-black">
      <div class="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 gap-8 md:grid-cols-4">
          <!-- Brand -->
          <div class="md:col-span-1">
            <h3 class="font-brutal text-lg text-brutal-white">DEFI.AI</h3>
            <p class="font-mono-brutal mt-2 text-sm text-brutal-white">
              AI-POWERED DEFI DOMINATION
            </p>
          </div>

          <!-- Supported Chains -->
          <div class="md:col-span-1">
            <h4 class="font-brutal text-sm uppercase text-neon-lime">SUPPORTED CHAINS</h4>
            <div class="mt-4 space-y-2">
              <div class="border-brutal bg-brutal-white px-3 py-1 font-mono-brutal text-xs text-brutal-black">
                ETHEREUM
              </div>
              <div class="border-brutal bg-brutal-charcoal px-3 py-1 font-mono-brutal text-xs text-brutal-white opacity-50">
                POLYGON (SOON)
              </div>
              <div class="border-brutal bg-brutal-charcoal px-3 py-1 font-mono-brutal text-xs text-brutal-white opacity-50">
                BASE (SOON)
              </div>
            </div>
          </div>

          <!-- Protocols -->
          <div class="md:col-span-1">
            <h4 class="font-brutal text-sm uppercase text-neon-cyan">PROTOCOLS</h4>
            <div class="mt-4 space-y-2">
              <div class="border-brutal bg-brutal-white px-3 py-1 font-mono-brutal text-xs text-brutal-black">
                AAVE
              </div>
              <div class="border-brutal bg-brutal-white px-3 py-1 font-mono-brutal text-xs text-brutal-black">
                UNISWAP V3
              </div>
              <div class="border-brutal bg-brutal-white px-3 py-1 font-mono-brutal text-xs text-brutal-black">
                LIDO
              </div>
            </div>
          </div>

          <!-- Links -->
          <div class="md:col-span-1">
            <h4 class="font-brutal text-sm uppercase text-neon-orange">LINKS</h4>
            <div class="mt-4 space-y-2">
              <NuxtLink
                to="/docs"
                class="block font-mono-brutal text-sm text-brutal-white hover:text-neon-lime"
              >
                DOCUMENTATION
              </NuxtLink>
              <NuxtLink
                to="/support"
                class="block font-mono-brutal text-sm text-brutal-white hover:text-neon-lime"
              >
                SUPPORT
              </NuxtLink>
              <NuxtLink
                to="/api"
                class="block font-mono-brutal text-sm text-brutal-white hover:text-neon-lime"
              >
                API ACCESS
              </NuxtLink>
            </div>
          </div>
        </div>

        <!-- Copyright -->
        <div class="border-brutal-thick mt-8 border-t pt-8">
          <p class="font-mono-brutal text-center text-sm text-brutal-white">
            © 2024 DEFI.AI - BUILT FOR THE FUTURE OF FINANCE
          </p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
// Mobile menu state
const isMobileMenuOpen = ref(false)

// Toggle mobile menu
const toggleMobileMenu = () => {
  isMobileMenuOpen.value = !isMobileMenuOpen.value
}

// Wallet connection (placeholder)
const connectWallet = () => {
  // TODO: Implement wallet connection logic
  console.log('Connect wallet clicked')
}

// Close mobile menu when route changes
watch(() => useRoute().path, () => {
  isMobileMenuOpen.value = false
})
</script>
