<script setup lang="ts">
// SEO and meta
useHead({
  title: 'DEFI.AI - AI-Powered DeFi Portfolio Management',
  meta: [
    {
      name: 'description',
      content:
        'Dominate DeFi with AI-powered portfolio management. Automated strategies, yield optimization, and risk management for Ethereum protocols.',
    },
  ],
});

// Wallet connection state
const isWalletConnected = ref(false);

// Connect wallet function
const connectWallet = () => {
  // TODO: Implement actual wallet connection
  isWalletConnected.value = true;
  console.log('Wallet connected');
};

// Feature data
const features = [
  {
    title: 'AI STRATEGY ENGINE',
    description:
      'Advanced reinforcement learning algorithms optimize your DeFi positions across Aave, Uniswap V3, and Lido.',
    accent: 'neon-lime',
    icon: 'brain',
  },
  {
    title: 'AUTOMATED REBALANCING',
    description:
      'Smart contract automation executes optimal portfolio adjustments based on market conditions and AI insights.',
    accent: 'neon-cyan',
    icon: 'refresh-cw',
  },
  {
    title: 'YIELD HARVESTING',
    description:
      'Maximize returns with automated reward claiming and compound optimization across multiple protocols.',
    accent: 'neon-orange',
    icon: 'trending-up',
  },
];

// Performance metrics (mock data)
const performanceMetrics = [
  { label: 'AVG APY', value: '24.7%', change: '+12.3%' },
  { label: 'TOTAL VALUE', value: '$2.4M', change: '+45.2%' },
  { label: 'PROTOCOLS', value: '3', change: 'ACTIVE' },
];
</script>

<template>
  <div class="min-h-screen bg-brutal-white">
    <!-- Hero Section -->
    <section class="relative overflow-hidden bg-brutal-white py-20">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="text-center">
          <!-- Main Headline -->
          <h1
            v-motion-slide-left
            class="font-brutal text-6xl text-brutal-black shadow-brutal-lg glitch md:text-8xl"
          >
            AI-POWERED
            <br />
            <span class="text-neon-lime">DEFI DOMINATION</span>
          </h1>

          <!-- Subheading -->
          <p
            v-motion-slide-right
            class="font-mono-brutal mx-auto mt-8 max-w-3xl text-xl text-brutal-black md:text-2xl"
          >
            &gt; AUTOMATED PORTFOLIO MANAGEMENT FOR THE FUTURE OF FINANCE
            <br />
            &gt; MAXIMIZE YIELDS • MINIMIZE RISK • OPTIMIZE EVERYTHING
          </p>

          <!-- CTA Button -->
          <div v-motion-pop-bottom class="mt-12">
            <Button
              class="border-brutal-heavy bg-neon-pink px-12 py-6 font-brutal text-xl uppercase text-brutal-white shadow-brutal-lg hover-brutal cursor-brutal"
              @click="connectWallet"
            >
              CONNECT WALLET
            </Button>
          </div>

          <!-- Performance Metrics -->
          <div class="mt-16 grid grid-cols-1 gap-6 md:grid-cols-3">
            <div
              v-for="metric in performanceMetrics"
              :key="metric.label"
              class="border-brutal bg-brutal-white p-6 shadow-brutal"
            >
              <div class="font-mono-brutal text-sm text-brutal-black">{{ metric.label }}</div>
              <div class="font-brutal text-3xl text-brutal-black">{{ metric.value }}</div>
              <div class="font-mono-brutal text-sm text-neon-lime">{{ metric.change }}</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="border-brutal-heavy bg-brutal-charcoal py-20">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="text-center">
          <h2 class="font-brutal text-4xl text-brutal-white md:text-6xl">
            FEATURES THAT
            <span class="text-neon-cyan">DOMINATE</span>
          </h2>
        </div>

        <div class="mt-16 grid grid-cols-1 gap-8 md:grid-cols-3">
          <div
            v-for="(feature, index) in features"
            :key="feature.title"
            v-motion-brutal-bounce
            :delay="index * 200"
            class="border-brutal bg-brutal-white p-8 shadow-brutal hover-brutal cursor-brutal"
          >
            <div class="mb-6">
              <div
                class="inline-flex h-16 w-16 items-center justify-center border-brutal"
                :class="'bg-' + feature.accent"
              >
                <Icon :name="'lucide:' + feature.icon" class="h-8 w-8 text-brutal-black" />
              </div>
            </div>
            <h3 class="font-brutal text-xl text-brutal-black">{{ feature.title }}</h3>
            <p class="font-mono-brutal mt-4 text-sm text-brutal-black">
              {{ feature.description }}
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Terminal Section -->
    <section class="bg-brutal-black py-20">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="text-center">
          <h2 class="font-brutal text-4xl text-brutal-white md:text-6xl">
            LIVE PORTFOLIO
            <span class="text-neon-orange">ANALYTICS</span>
          </h2>
        </div>

        <div v-motion-slide-left class="mt-16">
          <div class="border-brutal bg-brutal-black p-8 shadow-brutal-neon-lime hover-glow cursor-brutal">
            <div class="font-mono-brutal text-neon-lime">
              <div class="typing">$ defi-ai portfolio --analyze --optimize</div>
              <div class="mt-4 text-brutal-white">
                &gt; Analyzing portfolio composition...<br />
                &gt; Current APY: 24.7% (+12.3% vs benchmark)<br />
                &gt; Risk Score: 6.2/10 (MODERATE)<br />
                &gt; Optimization opportunities detected: 3<br />
                <br />
                &gt; RECOMMENDATION: Rebalance 15% from AAVE to LIDO<br />
                &gt; EXPECTED IMPACT: +2.1% APY, -0.3 risk score<br />
                &gt; GAS COST: ~$45 | BREAK-EVEN: 3.2 days<br />
                <br />
                <span class="text-neon-lime">✓ Ready to execute optimization</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="border-brutal-heavy bg-neon-lime py-20">
      <div class="mx-auto max-w-7xl px-4 text-center sm:px-6 lg:px-8">
        <h2 class="font-brutal text-4xl text-brutal-black md:text-6xl">
          START DOMINATING
          <br />
          DEFI TODAY
        </h2>
        <p class="font-mono-brutal mx-auto mt-8 max-w-2xl text-xl text-brutal-black">
          Join the future of automated portfolio management. Connect your wallet and let AI optimize your DeFi strategy.
        </p>
        <div v-motion-pop-bottom class="mt-12">
          <Button
            class="border-brutal-heavy bg-brutal-black px-12 py-6 font-brutal text-xl uppercase text-brutal-white shadow-brutal hover-brutal cursor-brutal"
            @click="connectWallet"
          >
            GET STARTED NOW
          </Button>
        </div>
      </div>
    </section>
  </div>
</template>
