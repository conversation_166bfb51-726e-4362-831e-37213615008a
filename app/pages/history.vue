<script setup lang="ts">
// SEO and meta
useHead({
  title: 'Transaction History - DEFI.AI',
  meta: [
    {
      name: 'description',
      content: 'Complete transaction history with filtering, search, and detailed analytics for your DeFi portfolio.'
    }
  ]
})

// Filter states
const selectedFilter = ref('ALL')
const searchQuery = ref('')
const selectedChain = ref('ETHEREUM')
const dateRange = ref({ start: '', end: '' })

// Filter options
const filterOptions = ['ALL', 'DEPOSIT', 'WITHDRAW', 'SWAP', 'HARVEST', 'REBALANCE']
const chainOptions = ['ETHEREUM', 'POLYGON', 'BASE']

// Transaction data (mock)
const transactions = ref([
  {
    id: '0x1234...5678',
    type: 'DEPOSIT',
    protocol: 'AAVE',
    amount: '+$50,000',
    token: 'USDC',
    timestamp: '2024-01-15 14:30:25',
    status: 'CONFIRMED',
    gasUsed: '0.0045 ETH',
    hash: '0x1234567890abcdef',
    chain: 'ETHEREUM'
  },
  {
    id: '0x2345...6789',
    type: 'REBALANCE',
    protocol: 'UNISWAP V3',
    amount: '+$25,000',
    token: 'ETH/USDC',
    timestamp: '2024-01-15 08:15:10',
    status: 'CONFIRMED',
    gasUsed: '0.0123 ETH',
    hash: '0x2345678901bcdefg',
    chain: 'ETHEREUM'
  },
  {
    id: '0x3456...7890',
    type: 'HARVEST',
    protocol: 'LIDO',
    amount: '+$1,234',
    token: 'stETH',
    timestamp: '2024-01-14 20:45:33',
    status: 'CONFIRMED',
    gasUsed: '0.0067 ETH',
    hash: '0x3456789012cdefgh',
    chain: 'ETHEREUM'
  },
  {
    id: '0x4567...8901',
    type: 'WITHDRAW',
    protocol: 'AAVE',
    amount: '-$10,000',
    token: 'USDC',
    timestamp: '2024-01-14 16:20:15',
    status: 'CONFIRMED',
    gasUsed: '0.0034 ETH',
    hash: '0x456789013defghij',
    chain: 'ETHEREUM'
  },
  {
    id: '0x5678...9012',
    type: 'SWAP',
    protocol: 'UNISWAP V3',
    amount: '$15,000',
    token: 'ETH → USDC',
    timestamp: '2024-01-14 12:10:45',
    status: 'CONFIRMED',
    gasUsed: '0.0089 ETH',
    hash: '0x56789014efghijkl',
    chain: 'ETHEREUM'
  },
  {
    id: '0x6789...0123',
    type: 'DEPOSIT',
    protocol: 'LIDO',
    amount: '+$30,000',
    token: 'ETH',
    timestamp: '2024-01-13 09:30:20',
    status: 'PENDING',
    gasUsed: '0.0056 ETH',
    hash: '0x67890125fghijklm',
    chain: 'ETHEREUM'
  }
])

// Computed filtered transactions
const filteredTransactions = computed(() => {
  return transactions.value.filter(tx => {
    const matchesFilter = selectedFilter.value === 'ALL' || tx.type === selectedFilter.value
    const matchesSearch = !searchQuery.value || 
      tx.protocol.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      tx.token.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      tx.hash.toLowerCase().includes(searchQuery.value.toLowerCase())
    const matchesChain = tx.chain === selectedChain.value
    
    return matchesFilter && matchesSearch && matchesChain
  })
})

// Transaction stats
const transactionStats = computed(() => {
  const total = filteredTransactions.value.length
  const confirmed = filteredTransactions.value.filter(tx => tx.status === 'CONFIRMED').length
  const pending = filteredTransactions.value.filter(tx => tx.status === 'PENDING').length
  const totalGas = filteredTransactions.value.reduce((sum, tx) => sum + parseFloat(tx.gasUsed.split(' ')[0]), 0)
  
  return {
    total,
    confirmed,
    pending,
    totalGas: totalGas.toFixed(4) + ' ETH'
  }
})

// Export transactions
const exportTransactions = () => {
  console.log('Exporting transactions:', filteredTransactions.value)
  // TODO: Implement CSV export
}

// View transaction details
const viewTransaction = (hash: string) => {
  console.log('Viewing transaction:', hash)
  // TODO: Open transaction details modal or navigate to explorer
}
</script>

<template>
  <div class="min-h-screen bg-brutal-charcoal">
    <!-- Header -->
    <div class="border-brutal-heavy bg-brutal-black px-4 py-6 sm:px-6 lg:px-8">
      <div class="mx-auto max-w-7xl">
        <h1 class="font-brutal text-4xl text-brutal-white md:text-6xl">
          TRANSACTION
          <span class="text-neon-orange">HISTORY</span>
        </h1>
        <p class="font-mono-brutal mt-2 text-brutal-white">
          > COMPLETE TRANSACTION LOG AND ANALYTICS
        </p>
      </div>
    </div>

    <div class="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
      <!-- Filters and Search -->
      <div class="mb-8">
        <Card class="border-brutal bg-brutal-white shadow-brutal">
          <CardContent class="p-6">
            <div class="grid grid-cols-1 gap-4 md:grid-cols-4">
              <!-- Transaction Type Filter -->
              <div>
                <label class="font-mono-brutal mb-2 block text-sm text-brutal-black">TYPE</label>
                <select
                  v-model="selectedFilter"
                  class="border-brutal w-full bg-brutal-charcoal p-3 font-mono-brutal text-brutal-white"
                >
                  <option
                    v-for="filter in filterOptions"
                    :key="filter"
                    :value="filter"
                  >
                    {{ filter }}
                  </option>
                </select>
              </div>

              <!-- Chain Filter -->
              <div>
                <label class="font-mono-brutal mb-2 block text-sm text-brutal-black">CHAIN</label>
                <select
                  v-model="selectedChain"
                  class="border-brutal w-full bg-brutal-charcoal p-3 font-mono-brutal text-brutal-white"
                >
                  <option
                    v-for="chain in chainOptions"
                    :key="chain"
                    :value="chain"
                  >
                    {{ chain }}
                  </option>
                </select>
              </div>

              <!-- Search -->
              <div>
                <label class="font-mono-brutal mb-2 block text-sm text-brutal-black">SEARCH</label>
                <input
                  v-model="searchQuery"
                  type="text"
                  placeholder="Protocol, token, hash..."
                  class="border-brutal w-full bg-brutal-charcoal p-3 font-mono-brutal text-brutal-white placeholder-gray-400"
                />
              </div>

              <!-- Export Button -->
              <div class="flex items-end">
                <Button
                  class="border-brutal bg-neon-lime px-4 py-3 font-brutal text-brutal-black shadow-brutal hover-brutal"
                  @click="exportTransactions"
                >
                  EXPORT CSV
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Transaction Stats -->
      <div class="mb-8 grid grid-cols-2 gap-4 md:grid-cols-4">
        <Card class="border-brutal bg-brutal-white shadow-brutal">
          <CardContent class="p-4">
            <div class="font-mono-brutal text-xs text-brutal-black">TOTAL TXS</div>
            <div class="font-brutal text-2xl text-brutal-black">{{ transactionStats.total }}</div>
          </CardContent>
        </Card>

        <Card class="border-brutal bg-brutal-white shadow-brutal">
          <CardContent class="p-4">
            <div class="font-mono-brutal text-xs text-brutal-black">CONFIRMED</div>
            <div class="font-brutal text-2xl text-neon-lime">{{ transactionStats.confirmed }}</div>
          </CardContent>
        </Card>

        <Card class="border-brutal bg-brutal-white shadow-brutal">
          <CardContent class="p-4">
            <div class="font-mono-brutal text-xs text-brutal-black">PENDING</div>
            <div class="font-brutal text-2xl text-neon-orange">{{ transactionStats.pending }}</div>
          </CardContent>
        </Card>

        <Card class="border-brutal bg-brutal-white shadow-brutal">
          <CardContent class="p-4">
            <div class="font-mono-brutal text-xs text-brutal-black">TOTAL GAS</div>
            <div class="font-brutal text-2xl text-brutal-black">{{ transactionStats.totalGas }}</div>
          </CardContent>
        </Card>
      </div>

      <!-- Transaction Table -->
      <div class="mb-8">
        <Card class="border-brutal bg-brutal-white shadow-brutal">
          <CardHeader>
            <CardTitle class="font-brutal text-brutal-black">TRANSACTION LOG</CardTitle>
          </CardHeader>
          <CardContent class="p-0">
            <!-- Table Header -->
            <div class="border-brutal-thick bg-brutal-black p-4">
              <div class="grid grid-cols-7 gap-4 font-mono-brutal text-sm text-brutal-white">
                <div>TYPE</div>
                <div>PROTOCOL</div>
                <div>AMOUNT</div>
                <div>TOKEN</div>
                <div>TIME</div>
                <div>STATUS</div>
                <div>ACTION</div>
              </div>
            </div>

            <!-- Transaction Rows -->
            <div class="max-h-96 overflow-y-auto">
              <div
                v-for="(tx, index) in filteredTransactions"
                :key="tx.id"
                class="border-brutal p-4"
                :class="{
                  'bg-brutal-white': index % 2 === 0,
                  'bg-brutal-charcoal': index % 2 === 1
                }"
              >
                <div class="grid grid-cols-7 gap-4 items-center">
                  <!-- Type -->
                  <div class="font-mono-brutal text-sm" :class="index % 2 === 0 ? 'text-brutal-black' : 'text-brutal-white'">
                    {{ tx.type }}
                  </div>

                  <!-- Protocol -->
                  <div class="font-mono-brutal text-sm" :class="index % 2 === 0 ? 'text-brutal-black' : 'text-brutal-white'">
                    {{ tx.protocol }}
                  </div>

                  <!-- Amount -->
                  <div
                    class="font-brutal text-sm"
                    :class="{
                      'text-neon-lime': tx.amount.startsWith('+'),
                      'text-neon-pink': tx.amount.startsWith('-'),
                      'text-brutal-black': !tx.amount.startsWith('+') && !tx.amount.startsWith('-') && index % 2 === 0,
                      'text-brutal-white': !tx.amount.startsWith('+') && !tx.amount.startsWith('-') && index % 2 === 1
                    }"
                  >
                    {{ tx.amount }}
                  </div>

                  <!-- Token -->
                  <div class="font-mono-brutal text-sm" :class="index % 2 === 0 ? 'text-brutal-black' : 'text-brutal-white'">
                    {{ tx.token }}
                  </div>

                  <!-- Time -->
                  <div class="font-mono-brutal text-xs" :class="index % 2 === 0 ? 'text-brutal-black' : 'text-brutal-white'">
                    {{ tx.timestamp }}
                  </div>

                  <!-- Status -->
                  <div
                    class="font-mono-brutal text-xs"
                    :class="{
                      'text-neon-lime': tx.status === 'CONFIRMED',
                      'text-neon-orange': tx.status === 'PENDING',
                      'text-neon-pink': tx.status === 'FAILED'
                    }"
                  >
                    {{ tx.status }}
                  </div>

                  <!-- Action -->
                  <div>
                    <button
                      class="border-brutal bg-neon-cyan px-2 py-1 font-mono-brutal text-xs text-brutal-black hover-brutal"
                      @click="viewTransaction(tx.hash)"
                    >
                      VIEW
                    </button>
                  </div>
                </div>

                <!-- Mobile Details (hidden on desktop) -->
                <div class="mt-2 block md:hidden">
                  <div class="font-mono-brutal text-xs" :class="index % 2 === 0 ? 'text-brutal-black' : 'text-brutal-white'">
                    Hash: {{ tx.hash }}
                  </div>
                  <div class="font-mono-brutal text-xs" :class="index % 2 === 0 ? 'text-brutal-black' : 'text-brutal-white'">
                    Gas: {{ tx.gasUsed }}
                  </div>
                </div>
              </div>
            </div>

            <!-- Empty State -->
            <div
              v-if="filteredTransactions.length === 0"
              class="border-brutal bg-brutal-charcoal p-8 text-center"
            >
              <div class="font-mono-brutal text-brutal-white">
                NO TRANSACTIONS FOUND
                <br />
                <div class="mt-4 text-xs">
                  ┌─────────────────────────┐
                  <br />
                  │   NO DATA AVAILABLE    │
                  <br />
                  │   TRY DIFFERENT FILTER │
                  <br />
                  └─────────────────────────┘
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Load More / Pagination -->
      <div class="text-center">
        <Button class="border-brutal bg-neon-pink px-8 py-3 font-brutal text-brutal-white shadow-brutal hover-brutal">
          LOAD MORE TRANSACTIONS
        </Button>
      </div>
    </div>
  </div>
</template>
