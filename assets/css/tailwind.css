@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  /* Neo-Brutalism Color Palette */
  --color-neon-lime: #00ff41;
  --color-neon-pink: #ff0080;
  --color-neon-cyan: #00ffff;
  --color-neon-orange: #ff6b00;
  --color-brutal-black: #000000;
  --color-brutal-charcoal: #1a1a1a;
  --color-brutal-white: #ffffff;
}

:root {
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --radius: 0.625rem;
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.145 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.145 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.985 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.269 0 0);
  --input: oklch(0.269 0 0);
  --ring: oklch(0.439 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.269 0 0);
  --sidebar-ring: oklch(0.439 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* Neo-Brutalism Typography */
  .font-brutal {
    font-family: "Inter", system-ui, -apple-system, sans-serif;
    font-weight: 900;
  }

  .font-mono-brutal {
    font-family: "JetBrains Mono", "Courier New", monospace;
    font-weight: 700;
  }
}

@layer utilities {
  /* Neo-Brutalism Borders */
  .border-brutal {
    border-width: 4px;
    border-color: var(--color-brutal-black);
    border-style: solid;
  }

  .border-brutal-thick {
    border-width: 6px;
    border-color: var(--color-brutal-black);
    border-style: solid;
  }

  .border-brutal-heavy {
    border-width: 8px;
    border-color: var(--color-brutal-black);
    border-style: solid;
  }

  /* Neo-Brutalism Shadows */
  .shadow-brutal {
    box-shadow: 8px 8px 0px var(--color-brutal-black);
  }

  .shadow-brutal-lg {
    box-shadow: 12px 12px 0px var(--color-brutal-black);
  }

  .shadow-brutal-neon-lime {
    box-shadow: 8px 8px 0px var(--color-neon-lime);
  }

  .shadow-brutal-neon-pink {
    box-shadow: 8px 8px 0px var(--color-neon-pink);
  }

  .shadow-brutal-neon-cyan {
    box-shadow: 8px 8px 0px var(--color-neon-cyan);
  }

  .shadow-brutal-neon-orange {
    box-shadow: 8px 8px 0px var(--color-neon-orange);
  }

  /* Neo-Brutalism Colors */
  .bg-neon-lime {
    background-color: var(--color-neon-lime);
  }

  .bg-neon-pink {
    background-color: var(--color-neon-pink);
  }

  .bg-neon-cyan {
    background-color: var(--color-neon-cyan);
  }

  .bg-neon-orange {
    background-color: var(--color-neon-orange);
  }

  .bg-brutal-black {
    background-color: var(--color-brutal-black);
  }

  .bg-brutal-charcoal {
    background-color: var(--color-brutal-charcoal);
  }

  .text-neon-lime {
    color: var(--color-neon-lime);
  }

  .text-neon-pink {
    color: var(--color-neon-pink);
  }

  .text-neon-cyan {
    color: var(--color-neon-cyan);
  }

  .text-neon-orange {
    color: var(--color-neon-orange);
  }

  .text-brutal-black {
    color: var(--color-brutal-black);
  }

  .text-brutal-white {
    color: var(--color-brutal-white);
  }

  /* Neo-Brutalism Animations */
  .glitch {
    animation: glitch 2s infinite;
  }

  @keyframes glitch {
    0%,
    100% {
      transform: translate(0);
    }
    10% {
      transform: translate(-2px, -2px);
    }
    20% {
      transform: translate(2px, 2px);
    }
    30% {
      transform: translate(-2px, 2px);
    }
    40% {
      transform: translate(2px, -2px);
    }
    50% {
      transform: translate(-2px, -2px);
    }
    60% {
      transform: translate(2px, 2px);
    }
    70% {
      transform: translate(-2px, 2px);
    }
    80% {
      transform: translate(2px, -2px);
    }
    90% {
      transform: translate(-2px, -2px);
    }
  }

  /* Terminal-style typing animation */
  .typing {
    overflow: hidden;
    border-right: 3px solid var(--color-neon-lime);
    white-space: nowrap;
    animation:
      typing 3.5s steps(40, end),
      blink-caret 0.75s step-end infinite;
  }

  @keyframes typing {
    from {
      width: 0;
    }
    to {
      width: 100%;
    }
  }

  @keyframes blink-caret {
    from,
    to {
      border-color: transparent;
    }
    50% {
      border-color: var(--color-neon-lime);
    }
  }

  /* Brutal hover effects */
  .hover-brutal:hover {
    transform: translate(-4px, -4px);
    box-shadow: 12px 12px 0px var(--color-brutal-black);
    transition: all 0.1s linear;
  }

  .hover-invert:hover {
    filter: invert(1);
    transition: all 0.1s linear;
  }
}
