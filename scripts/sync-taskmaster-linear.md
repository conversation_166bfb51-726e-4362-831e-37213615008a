# TaskMaster-Linear Synchronization Script

## Overview
This script provides commands and procedures for maintaining synchronization between TaskMaster MCP and Linear for the DeFi Agent project.

## Manual Sync Procedures

### 1. Update Linear Epic Status Based on TaskMaster Progress

#### Check TaskMaster Progress
```bash
# Get overall project status
get_tasks_taskmaster-ai --projectRoot /home/<USER>/code/defi-agent

# Get specific task details
get_task_taskmaster-ai --id 1 --projectRoot /home/<USER>/code/defi-agent
```

#### Update Linear Tickets
Use Linear MCP tools to update epic status based on TaskMaster completion:

```bash
# Example: Update DAP-10 (Infrastructure) based on Task 1 completion
linear --query "Update issue DAP-10 to Done state" --summary "Mark infrastructure epic as complete"
```

### 2. Break Down New Linear Epics into TaskMaster Tasks

#### When New Linear Epic is Created:
1. Get Linear epic details
2. Create corresponding TaskMaster tasks
3. Add cross-references

```bash
# Add new task based on Linear epic
add_task_taskmaster-ai --projectRoot /home/<USER>/code/defi-agent --prompt "Implement [Linear Epic Title] - Linear ID: DAP-XX"

# Expand task into subtasks
expand_task_taskmaster-ai --id [new_task_id] --projectRoot /home/<USER>/code/defi-agent
```

### 3. Weekly Progress Sync

#### Generate TaskMaster Progress Report
```bash
# Get completion statistics
get_tasks_taskmaster-ai --projectRoot /home/<USER>/code/defi-agent

# Generate complexity report
complexity_report_taskmaster-ai --projectRoot /home/<USER>/code/defi-agent
```

#### Update Linear Project Progress
1. Calculate completion percentages from TaskMaster
2. Update Linear epic descriptions with progress
3. Move completed epics to "Done" state

## Mapping Reference

### Linear Epic → TaskMaster Task Mapping

| Linear Epic | TaskMaster Tasks | Status Sync |
|-------------|------------------|-------------|
| DAP-10 (Infrastructure) | Tasks 1, 4, 25 | Task 1: done → DAP-10: Done |
| DAP-11 (Design System) | Task 2 | Task 2: pending → DAP-11: Todo |
| DAP-12 (Web3 Auth) | Tasks 3, 5 | Both pending → DAP-12: Todo |
| DAP-13 (DeFi Integration) | Task 7 | Task 7: pending → DAP-13: Todo |
| DAP-14 (AI Engine) | Tasks 11, 12, 13, 14 | All pending → DAP-14: Todo |
| DAP-15 (Portfolio Mgmt) | Tasks 15, 16, 17, 18 | All pending → DAP-15: Todo |
| DAP-16 (Data Pipeline) | Tasks 6, 10, 20 | All pending → DAP-16: Todo |
| DAP-17 (Security) | Tasks 19, 22, 23, 24 | All pending → DAP-17: Todo |

### Status Translation Rules

#### TaskMaster → Linear
- `done` → `Done`
- `in-progress` → `In Progress`
- `review` → `In Review`
- `pending` → `Todo`
- `cancelled` → `Canceled`

#### Completion Percentage Calculation
- Epic completion = (Completed TaskMaster tasks / Total TaskMaster tasks) * 100
- Update Linear epic description with percentage

## Automation Opportunities

### Future Enhancements
1. **GitHub Actions Integration**: Automatically sync status on PR merge
2. **Webhook Integration**: Real-time sync between systems
3. **Dashboard Creation**: Combined view of both systems
4. **Automated Reporting**: Weekly progress emails

### Current Manual Process
Until automation is implemented, use this checklist:

#### Daily (Developer)
- [ ] Update TaskMaster task status as work progresses
- [ ] Check for new Linear epics requiring TaskMaster breakdown

#### Weekly (Project Manager)
- [ ] Review TaskMaster completion statistics
- [ ] Update Linear epic progress and descriptions
- [ ] Generate stakeholder progress report
- [ ] Identify and resolve blockers

#### Monthly (Team)
- [ ] Review integration effectiveness
- [ ] Update mapping strategies
- [ ] Plan automation improvements
- [ ] Adjust workflows based on team feedback

## Troubleshooting

### Common Issues

#### 1. Status Mismatch
**Problem**: Linear shows "In Progress" but TaskMaster shows "pending"
**Solution**: Check subtask status in TaskMaster, update Linear accordingly

#### 2. Missing Cross-References
**Problem**: Can't find corresponding task/epic
**Solution**: Use mapping table in integration guide, add missing references

#### 3. Duplicate Work
**Problem**: Work tracked in both systems separately
**Solution**: Consolidate using mapping strategy, choose primary system per task type

### Best Practices

1. **Single Source of Truth**: Use TaskMaster for technical details, Linear for business progress
2. **Regular Sync**: Don't let systems drift apart for more than a week
3. **Clear Ownership**: Assign responsibility for maintaining sync
4. **Documentation**: Keep mapping table updated as project evolves

## Contact and Support

For questions about this integration:
- Technical issues: Check TaskMaster MCP documentation
- Project management: Use Linear support and documentation
- Integration problems: Refer to this guide or create team discussion
