# Lefthook configuration file
# See https://github.com/evilmartians/lefthook for more information

# Global settings
colors: true
no_tty: false

# Skip patterns for emergency commits
# Usage: LEFTHOOK_EXCLUDE=biome-check git commit -m "emergency fix"
skip_output:
  - meta
  - execution_info

# Pre-commit hooks - Main quality gate
pre-commit:
  parallel: true
  
  commands:
    # Primary Biome check - comprehensive code quality validation
    biome-check:
      # File patterns to include
      glob: "*.{js,jsx,ts,tsx,json,vue}"
      # Exclude patterns
      exclude: |
        *.min.js
        *.min.css
        node_modules/**/*
        dist/**/*
        .nuxt/**/*
        .output/**/*
        coverage/**/*
        public/**/*
      # Main command execution
      run: |
        echo "🔍 Running Biome code quality check..."
        echo "📁 Checking files: {staged_files}"
        bun run biome:check {staged_files}
      # Fail fast - stop other commands if this fails
      fail_text: |
        ❌ Biome check failed!
        
        Code quality issues detected. Please fix the issues above before committing.
        
        Quick fixes:
        • Run 'bun run biome:check' to see and auto-fix issues
        • Run 'bun run format' for formatting only
        • Use LEFTHOOK_EXCLUDE=biome-check git commit for emergency commits
        
      # Skip conditions
      skip:
        - merge
        - rebase
      # Environment variables for this command
      env:
        FORCE_COLOR: "1"
      # Priority (lower numbers run first)
      priority: 1

# Pre-push hooks - Final validation before push
pre-push:
  parallel: false
  
  commands:
    # Comprehensive CI check
    biome-ci:
      run: |
        echo "🚀 Running comprehensive Biome CI check..."
        echo "📊 This may take a moment for full project analysis..."
        bun run biome:ci
      fail_text: |
        ❌ Biome CI check failed!
        
        The full project has code quality issues that need to be addressed.
        Please run 'bun run biome:check' to fix issues before pushing.
        
      env:
        FORCE_COLOR: "1"

    # Build verification
    build-check:
      run: |
        echo "🏗️  Verifying project builds successfully..."
        bun run build
      fail_text: |
        ❌ Build failed!
        
        The project fails to build. Please fix build errors before pushing.
        
      env:
        FORCE_COLOR: "1"

# Commit message validation
commit-msg:
  commands:
    conventional-commit:
      run: |
        echo "📝 Validating commit message format..."
        # Basic conventional commit validation
        if ! grep -qE "^(feat|fix|docs|style|refactor|test|chore|perf|ci|build|revert)(\(.+\))?: .{1,50}" {1}; then
          echo "❌ Invalid commit message format!"
          echo ""
          echo "Please use conventional commit format:"
          echo "  type(scope): description"
          echo ""
          echo "Types: feat, fix, docs, style, refactor, test, chore, perf, ci, build, revert"
          echo "Example: feat(auth): add user authentication"
          echo "Example: fix: resolve navigation bug"
          exit 1
        fi
        echo "✅ Commit message format is valid"

# Post-checkout hook - Dependency management
post-checkout:
  commands:
    dependency-check:
      run: |
        if [ -f "package.json" ] && [ "package.json" -nt "node_modules/.package-lock.json" ] 2>/dev/null; then
          echo "📦 Dependencies may be outdated. Consider running 'bun install'"
        fi

# Emergency skip options
# Set environment variables to skip specific checks:
# 
# Skip all pre-commit hooks:
#   LEFTHOOK=0 git commit -m "emergency commit"
# 
# Skip specific command:
#   LEFTHOOK_EXCLUDE=biome-check git commit -m "skip biome check"
# 
# Skip multiple commands:
#   LEFTHOOK_EXCLUDE=biome-check,typescript-check git commit -m "skip checks"
#
# Force commit (skip all hooks):
#   git commit --no-verify -m "force commit"

# Performance optimizations
min_version: 1.5.0

# Source directories for faster file detection
source_dir: "."
source_dir_local: ".git"

# Automatically install hooks when lefthook is installed
install: true

# Output configuration
output:
  - execution
  - execution_out
  - execution_info
  - skips