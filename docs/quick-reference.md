# TaskMaster + Linear Quick Reference

## 🚀 Quick Access

### Linear Project
**URL**: https://linear.app/dappgenie/project/defi-agent-mvp-d7adaa785051
**Team**: Dappgenie
**Project**: DeFi Agent MVP

### TaskMaster Location
**Path**: `/home/<USER>/code/defi-agent/tasks/`
**Config**: `.taskmasterconfig`

## 📋 Epic Mapping

| Linear Epic | TaskMaster Tasks | Status |
|-------------|------------------|--------|
| DAP-10 Infrastructure | Tasks 1, 4, 25 | ✅ Done |
| DAP-11 Design System | Task 2 | 📋 Pending |
| DAP-12 Web3 Auth | Tasks 3, 5 | 📋 Pending |
| DAP-13 DeFi Integration | Task 7 | 📋 Pending |
| DAP-14 AI Engine | Tasks 11-14 | 📋 Pending |
| DAP-15 Portfolio Mgmt | Tasks 15-18 | 📋 Pending |
| DAP-16 Data Pipeline | Tasks 6, 10, 20 | 📋 Pending |
| DAP-17 Security | Tasks 19, 22-24 | 📋 Pending |

## 🔧 Common Commands

### TaskMaster MCP
```bash
# Check project status
get_tasks_taskmaster-ai --projectRoot /home/<USER>/code/defi-agent

# Get next task
next_task_taskmaster-ai --projectRoot /home/<USER>/code/defi-agent

# Update task status
set_task_status_taskmaster-ai --id X --status done --projectRoot /home/<USER>/code/defi-agent

# Add new task
add_task_taskmaster-ai --projectRoot /home/<USER>/code/defi-agent --prompt "Task description"

# Expand task into subtasks
expand_task_taskmaster-ai --id X --projectRoot /home/<USER>/code/defi-agent
```

### Linear MCP
```bash
# Get project issues
linear --query "Get all issues in project DeFi Agent MVP" --summary "Get project status"

# Update issue status
linear --query "Update issue DAP-X to Done state" --summary "Mark epic complete"

# Create new issue
linear --query "Create issue with title 'New Epic' in project DeFi Agent MVP" --summary "Add new epic"
```

## 👥 Role-Based Workflows

### Project Managers
1. 📊 Check Linear dashboard for epic progress
2. 📈 Review TaskMaster completion stats
3. 📝 Update stakeholders via Linear
4. 🔄 Sync status weekly

### Developers
1. 🎯 Get next task from TaskMaster
2. 💻 Work on implementation
3. ✅ Update TaskMaster status
4. 🔗 Reference Linear epic in commits

### AI Assistants
1. 🤖 Use TaskMaster MCP tools
2. 📋 Reference Linear context
3. 🔄 Maintain system sync
4. 📊 Generate progress reports

## 🔄 Status Mapping

| TaskMaster | Linear |
|------------|--------|
| pending | Todo |
| in-progress | In Progress |
| review | In Review |
| done | Done |
| cancelled | Canceled |

## 📚 Documentation

- **Integration Guide**: `docs/taskmaster-linear-integration.md`
- **Sync Procedures**: `scripts/sync-taskmaster-linear.md`
- **Current Status**: `docs/integration-status.md`
- **Quick Reference**: `docs/quick-reference.md` (this file)

## 🆘 Troubleshooting

### Can't find corresponding task/epic?
➡️ Check mapping table above

### Status out of sync?
➡️ Use sync procedures in `scripts/sync-taskmaster-linear.md`

### Need to add new epic?
➡️ Create in Linear first, then break down in TaskMaster

### TaskMaster command not working?
➡️ Check project root path: `/home/<USER>/code/defi-agent`

## 📞 Support

- **Linear**: https://linear.app/dappgenie
- **TaskMaster**: Check `.roo/rules/taskmaster.md`
- **Integration**: Review documentation in `docs/` folder
