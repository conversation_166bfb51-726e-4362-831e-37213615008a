{"$schema": "https://biomejs.dev/schemas/2.0.0-beta.5/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": true, "includes": ["**", "!**/node_modules", "!**/.nuxt", "!**/.output", "!**/.nitro", "!**/.cache", "!**/.wrangler", "!**/dist", "!**/*.lock"]}, "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2}, "assist": {"actions": {"source": {"organizeImports": "on"}}}, "linter": {"enabled": true, "rules": {"recommended": true, "correctness": {"noUnusedVariables": "off", "noUnusedImports": "off"}, "style": {"noParameterAssign": "error", "useAsConstAssertion": "error", "useDefaultParameterLast": "error", "useEnumInitializers": "error", "useSelfClosingElements": "error", "useSingleVarDeclarator": "error", "noUnusedTemplateLiteral": "error", "useNumberNamespace": "error", "noInferrableTypes": "error", "noUselessElse": "error"}}}, "javascript": {"formatter": {"quoteStyle": "single", "trailingCommas": "es5", "semicolons": "always"}}}